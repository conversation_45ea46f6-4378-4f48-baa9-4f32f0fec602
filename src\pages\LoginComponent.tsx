import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { InputText } from "primereact/inputtext";
import { Password } from "primereact/password";
import { But<PERSON> } from "primereact/button";
import { Checkbox } from "primereact/checkbox";
import { Toast } from "primereact/toast";
import { type User } from "../interfaces/user";

// --- Mocked services (replace with real implementations) ---
import authService  from "../services/authService";
//import { layoutService } from "../services/layoutService";



const Login: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<Toast>(null);

  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const [userName, setUserName] = useState<string>("");
  const [userPassword, setUserPassword] = useState<string>("");
  const [sessionTimer, setSessionTimer] = useState<NodeJS.Timeout | null>(null);

  const minute = 60 * 1000; // milliseconds

  //const dark: boolean = layoutService.config().colorScheme !== "light";

  const startSessTimer = useCallback(() => {
    if (sessionTimer) {
      clearTimeout(sessionTimer);
    }

    const timer = setTimeout(() => {
      console.log("Session has expired");
      navigate("/");
      setUserName("");
      setUserPassword("");
    }, 10 * minute);

    setSessionTimer(timer);
  }, [minute, navigate, sessionTimer]);

  const onLogin = (): void => {
    const user: User | undefined = authService.login(userName, userPassword);
    if (authService.isAuthenticated()) {
      startSessTimer();
      navigate("/dashboard");
    } else {
      toast.current?.show({
        severity: "error",
        summary: "Login Failed",
        detail: "Invalid Credentials!",
        life: 3000,
      });
    }
  };

  useEffect(() => {
    return () => {
      if (sessionTimer) {
        clearTimeout(sessionTimer);
      }
    };
  }, [sessionTimer]);

  return (
    <div className={`flex flex-col items-center justify-center min-h-screen p-6 ${dark ? "bg-gray-900 text-white" : "bg-white text-gray-900"}`}>
      <Toast ref={toast} />

      <div className="card w-full max-w-md shadow-lg p-6 rounded-2xl">
        <h2 className="text-2xl font-semibold text-center mb-6">Login</h2>

        <div className="mb-4">
          <span className="p-float-label w-full">
            <InputText
              id="username"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              className="w-full"
            />
            <label htmlFor="username">Username</label>
          </span>
        </div>

        <div className="mb-4">
          <span className="p-float-label w-full">
            <Password
              id="password"
              value={userPassword}
              onChange={(e) => setUserPassword(e.target.value)}
              feedback={false}
              toggleMask
              className="w-full"
            />
            <label htmlFor="password">Password</label>
          </span>
        </div>

        <div className="flex items-center mb-6">
          <Checkbox
            inputId="rememberMe"
            checked={rememberMe}
            onChange={(e) => setRememberMe(e.checked ?? false)}
            className="mr-2"
          />
          <label htmlFor="rememberMe">Remember Me</label>
        </div>

        <Button
          label="Login"
          icon="pi pi-sign-in"
          onClick={onLogin}
          className="w-full"
        />
      </div>
    </div>
  );
};

export default Login;
